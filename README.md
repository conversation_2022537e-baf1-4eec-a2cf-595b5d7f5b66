# About
BuySellBot is a tool built with Node.js that allows users to get buy and sell signals for various cryptocurrencies. It uses WebSocket to connect to the BitStamp API and retrieve real-time data. The data is then processed and stored in a Redis database. Users can connect to the WebSocket server and receive buy and sell signals for the cryptocurrencies they are interested in.

# How to run
1. Clone the repository
2. Install dependencies
    ```bash
    npm install
    ```
3. Run the server
    ```bash
    nodemon index.js
    ```
4. Connect to the WebSocket server  
    ```postman-request
    ws://localhost:8080/stream
    ```
5. Subscribe to the channels you are interested in
    ```json
    {
        "action": "subscribe",
        "channel": "btc"
    
    ```
6. Receive buy and sell signals